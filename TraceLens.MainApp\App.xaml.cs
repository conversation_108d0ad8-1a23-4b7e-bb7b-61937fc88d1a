﻿using System.Windows;
using Prism.Ioc;
using Prism.DryIoc;
using TraceLens.MainApp.Views;
using TraceLens.MainApp.ViewModels;
namespace TraceLens.MainApp
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : PrismApplication
    {
        protected override Window CreateShell()
        {
            return Container.Resolve<MainWindow>();
        }

        protected override void RegisterTypes(IContainerRegistry containerRegistry)
        {

            // Register view models
            containerRegistry.Register<MainWindowViewModel>();
        }
    }
}
