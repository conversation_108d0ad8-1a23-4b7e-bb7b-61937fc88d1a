<Window x:Class="TraceLens.ThemeControl.TestApp.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:hinput="clr-namespace:TraceLens.ThemeControl.Controls.HInput;assembly=TraceLens.ThemeControl"
        Title="HInput 测试应用" Height="600" Width="800">
    
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/TraceLens.ThemeControl;component/TraceLensTheme.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>
    
    <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="20">
        <StackPanel>
            
            <!-- 标题 -->
            <TextBlock Text="HInput 控件测试" 
                      FontSize="24" 
                      FontWeight="Bold" 
                      Foreground="{StaticResource TextPrimaryBrush}"
                      Margin="0,0,0,20"/>
            
            <!-- 基础用法 -->
            <GroupBox Header="基础用法" Margin="0,0,0,20">
                <StackPanel Margin="10">
                    <hinput:HInput Placeholder="请输入内容" Width="300" Margin="0,5"/>
                    <hinput:HInput Text="预设文本" Width="300" Margin="0,5"/>
                </StackPanel>
            </GroupBox>
            
            <!-- 不同状态 -->
            <GroupBox Header="不同状态" Margin="0,0,0,20">
                <StackPanel Margin="10">
                    <hinput:HInput Placeholder="正常状态" Width="300" Margin="0,5"/>
                    <hinput:HInput Placeholder="禁用状态" IsDisabled="True" Width="300" Margin="0,5"/>
                    <hinput:HInput Text="只读状态" IsReadOnly="True" Width="300" Margin="0,5"/>
                </StackPanel>
            </GroupBox>
            
            <!-- 功能特性 -->
            <GroupBox Header="功能特性" Margin="0,0,0,20">
                <StackPanel Margin="10">
                    <hinput:HInput Placeholder="可清除输入框" IsClearable="True" Width="300" Margin="0,5"/>
                    <hinput:HInput Placeholder="密码输入框" InputType="Password" ShowPassword="True" Width="300" Margin="0,5"/>
                    <hinput:HInput Placeholder="字数限制(10)" MaxLength="10" ShowWordLimit="True" Width="300" Margin="0,5"/>
                </StackPanel>
            </GroupBox>
            
            <!-- 带图标 -->
            <GroupBox Header="带图标" Margin="0,0,0,20">
                <StackPanel Margin="10">
                    <hinput:HInput Placeholder="搜索" PrefixIcon="search" Width="300" Margin="0,5"/>
                    <hinput:HInput Placeholder="日期" SuffixIcon="calendar" Width="300" Margin="0,5"/>
                    <hinput:HInput Placeholder="用户名" PrefixIcon="user" IsClearable="True" Width="300" Margin="0,5"/>
                </StackPanel>
            </GroupBox>
            
            <!-- 不同尺寸 -->
            <GroupBox Header="不同尺寸" Margin="0,0,0,20">
                <StackPanel Margin="10">
                    <hinput:HInput Placeholder="大尺寸" Size="Large" Width="300" Margin="0,5"/>
                    <hinput:HInput Placeholder="默认尺寸" Size="Default" Width="300" Margin="0,5"/>
                    <hinput:HInput Placeholder="小尺寸" Size="Small" Width="300" Margin="0,5"/>
                </StackPanel>
            </GroupBox>
            
        </StackPanel>
    </ScrollViewer>
    
</Window>
