using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace TraceLens.ThemeControl.Controls.HInput
{
    /// <summary>
    /// 字符串到可见性转换器
    /// 空字符串或null转换为Collapsed，非空字符串转换为Visible
    /// </summary>
    public class StringToVisibilityConverter : IValueConverter
    {
        /// <summary>
        /// 单例实例
        /// </summary>
        public static readonly StringToVisibilityConverter Instance = new StringToVisibilityConverter();

        /// <summary>
        /// 是否反转结果（空字符串显示为Visible，非空字符串显示为Collapsed）
        /// </summary>
        public bool IsInverted { get; set; } = false;

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            bool isEmpty = string.IsNullOrEmpty(value?.ToString());
            
            if (IsInverted)
            {
                return isEmpty ? Visibility.Visible : Visibility.Collapsed;
            }
            else
            {
                return isEmpty ? Visibility.Collapsed : Visibility.Visible;
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 反转的字符串到可见性转换器
    /// 空字符串或null转换为Visible，非空字符串转换为Collapsed
    /// </summary>
    public class InvertedStringToVisibilityConverter : StringToVisibilityConverter
    {
        /// <summary>
        /// 单例实例
        /// </summary>
        public static readonly new InvertedStringToVisibilityConverter Instance = new InvertedStringToVisibilityConverter();

        public InvertedStringToVisibilityConverter()
        {
            IsInverted = true;
        }
    }

    /// <summary>
    /// 整数到布尔值转换器
    /// 大于0的整数转换为true，否则为false
    /// </summary>
    public class IntToBoolConverter : IValueConverter
    {
        /// <summary>
        /// 单例实例
        /// </summary>
        public static readonly IntToBoolConverter Instance = new IntToBoolConverter();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is int intValue)
            {
                return intValue > 0;
            }
            return false;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
