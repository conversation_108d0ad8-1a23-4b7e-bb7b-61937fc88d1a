<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <!-- 主要功能颜色 -->
    <Color x:Key="PrimaryColor">#FF409EFF</Color>
    <Color x:Key="SuccessColor">#FF67C23A</Color>
    <Color x:Key="WarningColor">#FFE6A23C</Color>
    <Color x:Key="DangerColor">#FFF56C6C</Color>
    <Color x:Key="InfoColor">#FF909399</Color>

    <!-- 主要颜色的变体 -->
    <Color x:Key="PrimaryLightColor">#FF66B2FF</Color>
    <Color x:Key="PrimaryDarkColor">#FF337ECC</Color>
    <Color x:Key="SuccessLightColor">#FF85CE61</Color>
    <Color x:Key="SuccessDarkColor">#FF529B2E</Color>
    <Color x:Key="WarningLightColor">#FFEBB563</Color>
    <Color x:Key="WarningDarkColor">#FFB88230</Color>
    <Color x:Key="DangerLightColor">#FFF78989</Color>
    <Color x:Key="DangerDarkColor">#FFC45656</Color>

    <!-- 文本颜色 -->
    <Color x:Key="TextPrimaryColor">#FF303133</Color>
    <Color x:Key="TextRegularColor">#FF606266</Color>
    <Color x:Key="TextSecondaryColor">#FF909399</Color>
    <Color x:Key="TextPlaceholderColor">#FFC0C4CC</Color>

    <!-- 边框颜色 -->
    <Color x:Key="BorderBaseColor">#FFDCDFE6</Color>
    <Color x:Key="BorderLightColor">#FFE4E7ED</Color>
    <Color x:Key="BorderLighterColor">#FFEBEEF5</Color>
    <Color x:Key="BorderExtraLightColor">#FFF2F6FC</Color>

    <!-- 填充/背景颜色 -->
    <Color x:Key="FillBaseColor">#FFF0F2F5</Color>
    <Color x:Key="FillLightColor">#FFF5F7FA</Color>
    <Color x:Key="FillLighterColor">#FFFAFAFA</Color>
    <Color x:Key="FillExtraLightColor">#FFFFFFFF</Color>

    <!-- 兼容性别名 (保持向后兼容) -->
    <Color x:Key="AccentColor">#FF409EFF</Color>
    <Color x:Key="AccentLightColor">#FF66B2FF</Color>
    <Color x:Key="AccentDarkColor">#FF337ECC</Color>
    <Color x:Key="ErrorColor">#FFF56C6C</Color>
    <Color x:Key="BackgroundColor">#FFFFFFFF</Color>
    <Color x:Key="SurfaceColor">#FFF5F7FA</Color>
    <Color x:Key="CardColor">#FFFFFFFF</Color>
    <Color x:Key="TextDisabledColor">#FFC0C4CC</Color>
    <Color x:Key="BorderColor">#FFDCDFE6</Color>
    <Color x:Key="BorderDarkColor">#FFE4E7ED</Color>

    <!-- Element UI 画刷定义 -->

    <!-- 主要功能画刷 -->
    <SolidColorBrush x:Key="PrimaryBrush" Color="{StaticResource PrimaryColor}"/>
    <SolidColorBrush x:Key="PrimaryLightBrush" Color="{StaticResource PrimaryLightColor}"/>
    <SolidColorBrush x:Key="PrimaryDarkBrush" Color="{StaticResource PrimaryDarkColor}"/>

    <SolidColorBrush x:Key="SuccessBrush" Color="{StaticResource SuccessColor}"/>
    <SolidColorBrush x:Key="SuccessLightBrush" Color="{StaticResource SuccessLightColor}"/>
    <SolidColorBrush x:Key="SuccessDarkBrush" Color="{StaticResource SuccessDarkColor}"/>

    <SolidColorBrush x:Key="WarningBrush" Color="{StaticResource WarningColor}"/>
    <SolidColorBrush x:Key="WarningLightBrush" Color="{StaticResource WarningLightColor}"/>
    <SolidColorBrush x:Key="WarningDarkBrush" Color="{StaticResource WarningDarkColor}"/>

    <SolidColorBrush x:Key="DangerBrush" Color="{StaticResource DangerColor}"/>
    <SolidColorBrush x:Key="DangerLightBrush" Color="{StaticResource DangerLightColor}"/>
    <SolidColorBrush x:Key="DangerDarkBrush" Color="{StaticResource DangerDarkColor}"/>

    <SolidColorBrush x:Key="InfoBrush" Color="{StaticResource InfoColor}"/>

    <!-- 文本画刷 -->
    <SolidColorBrush x:Key="TextPrimaryBrush" Color="{StaticResource TextPrimaryColor}"/>
    <SolidColorBrush x:Key="TextRegularBrush" Color="{StaticResource TextRegularColor}"/>
    <SolidColorBrush x:Key="TextSecondaryBrush" Color="{StaticResource TextSecondaryColor}"/>
    <SolidColorBrush x:Key="TextPlaceholderBrush" Color="{StaticResource TextPlaceholderColor}"/>

    <!-- 边框画刷 -->
    <SolidColorBrush x:Key="BorderBaseBrush" Color="{StaticResource BorderBaseColor}"/>
    <SolidColorBrush x:Key="BorderLightBrush" Color="{StaticResource BorderLightColor}"/>
    <SolidColorBrush x:Key="BorderLighterBrush" Color="{StaticResource BorderLighterColor}"/>
    <SolidColorBrush x:Key="BorderExtraLightBrush" Color="{StaticResource BorderExtraLightColor}"/>

    <!-- 填充/背景画刷 -->
    <SolidColorBrush x:Key="FillBaseBrush" Color="{StaticResource FillBaseColor}"/>
    <SolidColorBrush x:Key="FillLightBrush" Color="{StaticResource FillLightColor}"/>
    <SolidColorBrush x:Key="FillLighterBrush" Color="{StaticResource FillLighterColor}"/>
    <SolidColorBrush x:Key="FillExtraLightBrush" Color="{StaticResource FillExtraLightColor}"/>

    <!-- 兼容性别名画刷 (保持向后兼容) -->
    <SolidColorBrush x:Key="AccentBrush" Color="{StaticResource AccentColor}"/>
    <SolidColorBrush x:Key="AccentLightBrush" Color="{StaticResource AccentLightColor}"/>
    <SolidColorBrush x:Key="AccentDarkBrush" Color="{StaticResource AccentDarkColor}"/>
    <SolidColorBrush x:Key="ErrorBrush" Color="{StaticResource ErrorColor}"/>
    <SolidColorBrush x:Key="BackgroundBrush" Color="{StaticResource BackgroundColor}"/>
    <SolidColorBrush x:Key="SurfaceBrush" Color="{StaticResource SurfaceColor}"/>
    <SolidColorBrush x:Key="CardBrush" Color="{StaticResource CardColor}"/>
    <SolidColorBrush x:Key="TextDisabledBrush" Color="{StaticResource TextDisabledColor}"/>
    <SolidColorBrush x:Key="BorderBrush" Color="{StaticResource BorderColor}"/>
    <SolidColorBrush x:Key="BorderDarkBrush" Color="{StaticResource BorderDarkColor}"/>

</ResourceDictionary>
