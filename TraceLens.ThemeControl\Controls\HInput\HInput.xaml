<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="clr-namespace:TraceLens.ThemeControl.Controls.HInput"
                    xmlns:hicon="clr-namespace:TraceLens.ThemeControl.Controls.HIcon"
                    xmlns:converters="clr-namespace:TraceLens.ThemeControl.Converters">

    <!-- HInput 图标按钮样式 -->
    <Style x:Key="HInputIconButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="0"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="2">
                        <ContentPresenter HorizontalAlignment="Center"
                                        VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{StaticResource FillLightBrush}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="{StaticResource FillBaseBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- HInput 控件样式 -->
    <Style TargetType="{x:Type local:HInput}">
        <Setter Property="Background" Value="{StaticResource FillExtraLightBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBaseBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource TextRegularBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontFamily" Value="Segoe UI, Microsoft YaHei, sans-serif"/>
        <Setter Property="Padding" Value="12,8"/>
        <Setter Property="Height" Value="32"/>
        <Setter Property="MinWidth" Value="120"/>
        <Setter Property="HorizontalContentAlignment" Value="Left"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type local:HInput}">
                    <Border x:Name="PART_Border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="4"
                            SnapsToDevicePixels="True">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <!-- 前缀图标 -->
                                <ColumnDefinition Width="Auto"/>
                                <!-- 输入框内容 -->
                                <ColumnDefinition Width="*"/>
                                <!-- 后缀图标区域 -->
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <!-- 前缀图标 -->
                            <hicon:HIcon x:Name="PART_PrefixIcon"
                                        Grid.Column="0"
                                        IconName="{TemplateBinding PrefixIcon}"
                                        IconSize="14"
                                        IconColor="{StaticResource TextSecondaryBrush}"
                                        Margin="8,0,4,0"
                                        VerticalAlignment="Center"
                                        Visibility="Collapsed"/>

                            <!-- 输入框容器 -->
                            <Grid Grid.Column="1">
                                <!-- 占位符文本 -->
                                <TextBlock x:Name="PART_Placeholder"
                                          Text="{TemplateBinding Placeholder}"
                                          Foreground="{StaticResource TextPlaceholderBrush}"
                                          FontSize="{TemplateBinding FontSize}"
                                          FontFamily="{TemplateBinding FontFamily}"
                                          Margin="{TemplateBinding Padding}"
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                          IsHitTestVisible="False"
                                          Visibility="Visible"/>

                                <!-- 输入框 -->
                                <TextBox x:Name="PART_TextBox"
                                        Text="{TemplateBinding Text}"
                                        FontSize="{TemplateBinding FontSize}"
                                        FontFamily="{TemplateBinding FontFamily}"
                                        Foreground="{TemplateBinding Foreground}"
                                        Background="Transparent"
                                        BorderThickness="0"
                                        Padding="{TemplateBinding Padding}"
                                        VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                        HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                        VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                        HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                        IsReadOnly="{TemplateBinding IsReadOnly}"
                                        MaxLength="{TemplateBinding MaxLength}"
                                        CaretBrush="{StaticResource PrimaryBrush}"/>

                                <!-- 密码框 -->
                                <PasswordBox x:Name="PART_PasswordBox"
                                            FontSize="{TemplateBinding FontSize}"
                                            FontFamily="{TemplateBinding FontFamily}"
                                            Foreground="{TemplateBinding Foreground}"
                                            Background="Transparent"
                                            BorderThickness="0"
                                            Padding="{TemplateBinding Padding}"
                                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                            VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                            HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                            MaxLength="{TemplateBinding MaxLength}"
                                            Visibility="Collapsed"/>
                            </Grid>

                            <!-- 后缀图标区域 -->
                            <StackPanel Grid.Column="2"
                                       Orientation="Horizontal"
                                       VerticalAlignment="Center">

                                <!-- 清除按钮 -->
                                <Button x:Name="PART_ClearButton"
                                       Width="16"
                                       Height="16"
                                       Margin="4,0"
                                       Background="Transparent"
                                       BorderThickness="0"
                                       Cursor="Hand"
                                       Visibility="Collapsed"
                                       ToolTip="清除"
                                       Style="{StaticResource HInputIconButtonStyle}">
                                    <hicon:HIcon IconName="times-circle"
                                                IconSize="14"
                                                IconColor="{StaticResource TextSecondaryBrush}"/>
                                </Button>

                                <!-- 密码切换按钮 -->
                                <Button x:Name="PART_PasswordToggleButton"
                                       Width="16"
                                       Height="16"
                                       Margin="4,0"
                                       Background="Transparent"
                                       BorderThickness="0"
                                       Cursor="Hand"
                                       Visibility="Collapsed"
                                       ToolTip="显示/隐藏密码"
                                       Style="{StaticResource HInputIconButtonStyle}">
                                    <hicon:HIcon x:Name="PART_PasswordToggleIcon"
                                                IconName="eye"
                                                IconSize="14"
                                                IconColor="{StaticResource TextSecondaryBrush}"/>
                                </Button>

                                <!-- 后缀图标 -->
                                <hicon:HIcon x:Name="PART_SuffixIcon"
                                            IconName="{TemplateBinding SuffixIcon}"
                                            IconSize="14"
                                            IconColor="{StaticResource TextSecondaryBrush}"
                                            Margin="4,0,8,0"
                                            VerticalAlignment="Center"
                                            Visibility="Collapsed"/>
                            </StackPanel>

                            <!-- 字数统计 -->
                            <TextBlock x:Name="PART_WordLimit"
                                      Grid.Column="2"
                                      FontSize="12"
                                      Foreground="{StaticResource TextSecondaryBrush}"
                                      Margin="0,0,8,0"
                                      VerticalAlignment="Bottom"
                                      HorizontalAlignment="Right"
                                      Visibility="Collapsed"/>
                        </Grid>
                    </Border>

                    <ControlTemplate.Triggers>
                        <!-- 占位符显示/隐藏 -->
                        <DataTrigger Binding="{Binding Text, RelativeSource={RelativeSource Self}, Converter={x:Static converters:InvertedStringToVisibilityConverter.Instance}}" Value="Collapsed">
                            <Setter TargetName="PART_Placeholder" Property="Visibility" Value="Collapsed"/>
                        </DataTrigger>

                        <!-- 前缀图标显示 -->
                        <DataTrigger Binding="{Binding PrefixIcon, RelativeSource={RelativeSource Self}, Converter={x:Static converters:StringToVisibilityConverter.Instance}}" Value="Visible">
                            <Setter TargetName="PART_PrefixIcon" Property="Visibility" Value="Visible"/>
                        </DataTrigger>

                        <!-- 后缀图标显示 -->
                        <DataTrigger Binding="{Binding SuffixIcon, RelativeSource={RelativeSource Self}, Converter={x:Static converters:StringToVisibilityConverter.Instance}}" Value="Visible">
                            <Setter TargetName="PART_SuffixIcon" Property="Visibility" Value="Visible"/>
                        </DataTrigger>

                        <!-- 字数统计显示 -->
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding ShowWordLimit, RelativeSource={RelativeSource Self}}" Value="True"/>
                                <Condition Binding="{Binding MaxLength, RelativeSource={RelativeSource Self}, Converter={x:Static converters:IntToBoolConverter.Instance}}" Value="True"/>
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="PART_WordLimit" Property="Visibility" Value="Visible"/>
                        </MultiDataTrigger>

                        <!-- 密码框显示 -->
                        <Trigger Property="InputType" Value="Password">
                            <Setter TargetName="PART_TextBox" Property="Visibility" Value="Collapsed"/>
                            <Setter TargetName="PART_PasswordBox" Property="Visibility" Value="Visible"/>
                        </Trigger>

                        <!-- 鼠标悬停状态 -->
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="PART_Border" Property="BorderBrush" Value="{StaticResource BorderLightBrush}"/>
                        </Trigger>

                        <!-- 聚焦状态 -->
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsKeyboardFocusWithin" Value="True"/>
                                <Condition Property="IsDisabled" Value="False"/>
                            </MultiTrigger.Conditions>
                            <Setter TargetName="PART_Border" Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                            <Setter TargetName="PART_Border" Property="BorderThickness" Value="1"/>
                        </MultiTrigger>

                        <!-- 禁用状态 -->
                        <Trigger Property="IsDisabled" Value="True">
                            <Setter Property="Background" Value="{StaticResource FillLightBrush}"/>
                            <Setter Property="Foreground" Value="{StaticResource TextDisabledBrush}"/>
                            <Setter TargetName="PART_Border" Property="BorderBrush" Value="{StaticResource BorderLighterBrush}"/>
                            <Setter TargetName="PART_TextBox" Property="IsEnabled" Value="False"/>
                            <Setter TargetName="PART_PasswordBox" Property="IsEnabled" Value="False"/>
                        </Trigger>

                        <!-- 只读状态 -->
                        <Trigger Property="IsReadOnly" Value="True">
                            <Setter Property="Background" Value="{StaticResource FillLightBrush}"/>
                            <Setter TargetName="PART_Border" Property="BorderBrush" Value="{StaticResource BorderLighterBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>

        <!-- 尺寸样式 -->
        <Style.Triggers>
            <!-- 小尺寸 -->
            <Trigger Property="Size" Value="Small">
                <Setter Property="Height" Value="24"/>
                <Setter Property="FontSize" Value="12"/>
                <Setter Property="Padding" Value="8,4"/>
            </Trigger>

            <!-- 大尺寸 -->
            <Trigger Property="Size" Value="Large">
                <Setter Property="Height" Value="40"/>
                <Setter Property="FontSize" Value="16"/>
                <Setter Property="Padding" Value="16,12"/>
            </Trigger>
        </Style.Triggers>
    </Style>

</ResourceDictionary>
