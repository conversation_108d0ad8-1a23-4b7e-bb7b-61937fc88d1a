<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <!-- FontAwesome 字体定义 -->
    <FontFamily x:Key="FontAwesome">pack://application:,,,/TraceLens.ThemeControl;component/Assets/fonts/fontawesome-webfont.ttf#FontAwesome</FontFamily>

    <!-- 在 Windows 上，我们使用最接近的字体替代 -->
    <FontFamily x:Key="IndustrialFont">Segoe UI, Helvetica Neue, Helvetica, Microsoft YaHei, Arial, sans-serif</FontFamily>
    <FontFamily x:Key="MonospaceFont">Consolas, Monaco, Courier New, monospace</FontFamily>
    
    <system:Double x:Key="FontSizeExtraSmall" xmlns:system="clr-namespace:System;assembly=mscorlib">12</system:Double>
    <system:Double x:Key="FontSizeSmall" xmlns:system="clr-namespace:System;assembly=mscorlib">13</system:Double>
    <system:Double x:Key="FontSizeBase" xmlns:system="clr-namespace:System;assembly=mscorlib">14</system:Double>
    <system:Double x:Key="FontSizeMedium" xmlns:system="clr-namespace:System;assembly=mscorlib">16</system:Double>
    <system:Double x:Key="FontSizeLarge" xmlns:system="clr-namespace:System;assembly=mscorlib">18</system:Double>
    <system:Double x:Key="FontSizeExtraLarge" xmlns:system="clr-namespace:System;assembly=mscorlib">20</system:Double>
    
    <system:Double x:Key="LineHeightNone" xmlns:system="clr-namespace:System;assembly=mscorlib">1.0</system:Double>
    <system:Double x:Key="LineHeightCompact" xmlns:system="clr-namespace:System;assembly=mscorlib">1.3</system:Double>
    <system:Double x:Key="LineHeightRegular" xmlns:system="clr-namespace:System;assembly=mscorlib">1.5</system:Double>
    <system:Double x:Key="LineHeightLoose" xmlns:system="clr-namespace:System;assembly=mscorlib">1.7</system:Double>

    <!-- 字体权重定义 -->
    <FontWeight x:Key="FontWeightNormal">Normal</FontWeight>
    <FontWeight x:Key="FontWeightMedium">Medium</FontWeight>
    <FontWeight x:Key="FontWeightSemiBold">SemiBold</FontWeight>
    <FontWeight x:Key="FontWeightBold">Bold</FontWeight>

    <!-- 图标字体大小 -->
    <system:Double x:Key="IconSizeSmall" xmlns:system="clr-namespace:System;assembly=mscorlib">14</system:Double>
    <system:Double x:Key="IconSizeMedium" xmlns:system="clr-namespace:System;assembly=mscorlib">16</system:Double>
    <system:Double x:Key="IconSizeLarge" xmlns:system="clr-namespace:System;assembly=mscorlib">20</system:Double>
    <system:Double x:Key="IconSizeExtraLarge" xmlns:system="clr-namespace:System;assembly=mscorlib">24</system:Double>

</ResourceDictionary>
