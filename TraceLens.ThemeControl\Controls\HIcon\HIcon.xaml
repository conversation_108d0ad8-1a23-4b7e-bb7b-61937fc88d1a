<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:controls="clr-namespace:TraceLens.ThemeControl.Controls.HIcon">

    <!-- HIcon 控件样式 -->
    <Style TargetType="{x:Type controls:HIcon}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type controls:HIcon}">
                    <TextBlock x:Name="PART_IconText"
                               FontFamily="pack://application:,,,/TraceLens.ThemeControl;component/Assets/fonts/fontawesome-webfont.ttf#FontAwesome"
                               FontSize="{TemplateBinding IconSize}"
                               Foreground="{TemplateBinding IconColor}"
                               Text="{TemplateBinding Content}"
                               HorizontalAlignment="Center"
                               VerticalAlignment="Center"
                               TextAlignment="Center"/>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        
        <!-- 默认属性设置 -->
        <!-- IconSize 和 IconColor 都使用依赖属性的默认值，不再依赖可能不存在的资源 -->
        <!-- IconSize 默认值: 16.0, IconColor 默认值: Brushes.Black -->
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="Width" Value="Auto"/>
        <Setter Property="Height" Value="Auto"/>
    </Style>

    <!-- HIcon 预定义大小样式 -->
    
    <!-- 小图标样式 -->
    <Style x:Key="HIconSmall" TargetType="{x:Type controls:HIcon}" BasedOn="{StaticResource {x:Type controls:HIcon}}">
        <Setter Property="IconSize" Value="12"/>
    </Style>

    <!-- 中等图标样式 -->
    <Style x:Key="HIconMedium" TargetType="{x:Type controls:HIcon}" BasedOn="{StaticResource {x:Type controls:HIcon}}">
        <Setter Property="IconSize" Value="16"/>
    </Style>

    <!-- 大图标样式 -->
    <Style x:Key="HIconLarge" TargetType="{x:Type controls:HIcon}" BasedOn="{StaticResource {x:Type controls:HIcon}}">
        <Setter Property="IconSize" Value="24"/>
    </Style>

    <!-- 超大图标样式 -->
    <Style x:Key="HIconExtraLarge" TargetType="{x:Type controls:HIcon}" BasedOn="{StaticResource {x:Type controls:HIcon}}">
        <Setter Property="IconSize" Value="32"/>
    </Style>

    <!-- HIcon 预定义颜色样式 -->
    
    <!-- 主色调图标 -->
    <Style x:Key="HIconPrimary" TargetType="{x:Type controls:HIcon}" BasedOn="{StaticResource {x:Type controls:HIcon}}">
        <Setter Property="IconColor" Value="{StaticResource PrimaryBrush}"/>
    </Style>
    
    <!-- 成功色图标 -->
    <Style x:Key="HIconSuccess" TargetType="{x:Type controls:HIcon}" BasedOn="{StaticResource {x:Type controls:HIcon}}">
        <Setter Property="IconColor" Value="{StaticResource SuccessBrush}"/>
    </Style>
    
    <!-- 警告色图标 -->
    <Style x:Key="HIconWarning" TargetType="{x:Type controls:HIcon}" BasedOn="{StaticResource {x:Type controls:HIcon}}">
        <Setter Property="IconColor" Value="{StaticResource WarningBrush}"/>
    </Style>
    
    <!-- 危险色图标 -->
    <Style x:Key="HIconDanger" TargetType="{x:Type controls:HIcon}" BasedOn="{StaticResource {x:Type controls:HIcon}}">
        <Setter Property="IconColor" Value="{StaticResource DangerBrush}"/>
    </Style>
    
    <!-- 信息色图标 -->
    <Style x:Key="HIconInfo" TargetType="{x:Type controls:HIcon}" BasedOn="{StaticResource {x:Type controls:HIcon}}">
        <Setter Property="IconColor" Value="{StaticResource InfoBrush}"/>
    </Style>
    
    <!-- 次要文本色图标 -->
    <Style x:Key="HIconSecondary" TargetType="{x:Type controls:HIcon}" BasedOn="{StaticResource {x:Type controls:HIcon}}">
        <Setter Property="IconColor" Value="{StaticResource TextSecondaryBrush}"/>
    </Style>

</ResourceDictionary>
